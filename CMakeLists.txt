cmake_minimum_required(VERSION 3.16)
project(HVNC)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set Windows-specific settings
if(WIN32)
    add_definitions(-DWIN32 -D_WINDOWS -DSECURITY_WIN32)
    # Disable specific warnings
    add_compile_options(/wd4267 /wd4244 /wd4533)
endif()

# Add common library
add_subdirectory(common)

# Add Server executable
add_subdirectory(Server)

# Add Client executable  
add_subdirectory(Client)

# Set startup project (equivalent to Visual Studio startup project)
set_property(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR} PROPERTY VS_STARTUP_PROJECT Server)
