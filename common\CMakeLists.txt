# Common library CMakeLists.txt
set(COMMON_SOURCES
    Api.cpp
    HTTP.cpp
    Panel.cpp
    Utils.cpp
)

set(COMMON_HEADERS
    Api.h
    Common.h
    HTTP.h
    Inject.h
    Panel.h
    Utils.h
)

# Create static library for common code
add_library(common STATIC ${COMMON_SOURCES} ${COMMON_HEADERS})

# Set include directories
target_include_directories(common PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})

# Link Windows libraries
if(WIN32)
    target_link_libraries(common 
        ws2_32
        wininet
        shlwapi
        shell32
        secur32
        advapi32
        version
        psapi
        gdi32
        user32
        kernel32
    )
endif()

# Set compiler-specific options
if(MSVC)
    target_compile_options(common PRIVATE /MT$<$<CONFIG:Debug>:d>)
    target_compile_definitions(common PRIVATE WIN32 _WINDOWS SECURITY_WIN32)
endif()
