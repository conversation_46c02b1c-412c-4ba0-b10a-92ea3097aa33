# Client executable CMakeLists.txt
set(CLIENT_SOURCES
    Main.cpp
    HiddenDesktop.cpp
)

set(CLIENT_HEADERS
    HiddenDesktop.h
)

# Create Client executable
add_executable(Client ${CLIENT_SOURCES} ${CLIENT_HEADERS})

# Link with common library
target_link_libraries(Client common)

# Set include directories
target_include_directories(Client PRIVATE 
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/common
)

# Set Windows-specific properties
if(WIN32)
    # Link additional Windows libraries
    target_link_libraries(Client
        ws2_32
        user32
        kernel32
        gdi32
        shell32
    )
endif()

# Set compiler-specific options
if(MSVC)
    target_compile_options(Client PRIVATE /MT$<$<CONFIG:Debug>:d>)
    target_compile_definitions(Client PRIVATE WIN32 _WINDOWS)
endif()

# Set output directory
set_target_properties(Client PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY_DEBUG "${CMAKE_SOURCE_DIR}/_bin/Debug/Win32"
    RUNTIME_OUTPUT_DIRECTORY_RELEASE "${CMAKE_SOURCE_DIR}/_bin/Release/Win32"
)
