
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
      Build started 7/11/2025 10:11:35 AM.
      
      Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\3.31.7\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\3.31.7\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\3.31.7\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.26
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/Users/<USER>/OneDrive/Desktop/fromScratch/HVNC/build/CMakeFiles/3.31.7/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
      Build started 7/11/2025 10:11:36 AM.
      
      Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\3.31.7\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\3.31.7\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\3.31.7\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.92
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/OneDrive/Desktop/fromScratch/HVNC/build/CMakeFiles/3.31.7/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/fromScratch/HVNC/build/CMakeFiles/CMakeScratch/TryCompile-99eh9e"
      binary: "C:/Users/<USER>/OneDrive/Desktop/fromScratch/HVNC/build/CMakeFiles/CMakeScratch/TryCompile-99eh9e"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/fromScratch/HVNC/build/CMakeFiles/CMakeScratch/TryCompile-99eh9e'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_2f81a.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 7/11/2025 10:11:38 AM.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-99eh9e\\cmTC_2f81a.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_2f81a.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-99eh9e\\Debug\\".
          Creating directory "cmTC_2f81a.dir\\Debug\\cmTC_2f81a.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_2f81a.dir\\Debug\\cmTC_2f81a.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_2f81a.dir\\Debug\\cmTC_2f81a.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_2f81a.dir\\Debug\\\\" /Fd"cmTC_2f81a.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_2f81a.dir\\Debug\\\\" /Fd"cmTC_2f81a.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-99eh9e\\Debug\\cmTC_2f81a.exe" /INCREMENTAL /ILK:"cmTC_2f81a.dir\\Debug\\cmTC_2f81a.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/fromScratch/HVNC/build/CMakeFiles/CMakeScratch/TryCompile-99eh9e/Debug/cmTC_2f81a.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/fromScratch/HVNC/build/CMakeFiles/CMakeScratch/TryCompile-99eh9e/Debug/cmTC_2f81a.lib" /MACHINE:X64  /machine:x64 cmTC_2f81a.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_2f81a.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-99eh9e\\Debug\\cmTC_2f81a.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_2f81a.dir\\Debug\\cmTC_2f81a.tlog\\unsuccessfulbuild".
          Touching "cmTC_2f81a.dir\\Debug\\cmTC_2f81a.tlog\\cmTC_2f81a.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-99eh9e\\cmTC_2f81a.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.05
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35207.1
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/fromScratch/HVNC/build/CMakeFiles/CMakeScratch/TryCompile-npj5it"
      binary: "C:/Users/<USER>/OneDrive/Desktop/fromScratch/HVNC/build/CMakeFiles/CMakeScratch/TryCompile-npj5it"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/fromScratch/HVNC/build/CMakeFiles/CMakeScratch/TryCompile-npj5it'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_1f8c8.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 7/11/2025 10:11:40 AM.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-npj5it\\cmTC_1f8c8.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_1f8c8.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-npj5it\\Debug\\".
          Creating directory "cmTC_1f8c8.dir\\Debug\\cmTC_1f8c8.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_1f8c8.dir\\Debug\\cmTC_1f8c8.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_1f8c8.dir\\Debug\\cmTC_1f8c8.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_1f8c8.dir\\Debug\\\\" /Fd"cmTC_1f8c8.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_1f8c8.dir\\Debug\\\\" /Fd"cmTC_1f8c8.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp(1,1): error C1090: PDB API call failed, error code '3': C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-npj5it\\cmTC_1f8c8.dir\\Debug\\vc143.pdb [C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-npj5it\\cmTC_1f8c8.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-npj5it\\cmTC_1f8c8.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-npj5it\\cmTC_1f8c8.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp(1,1): error C1090: PDB API call failed, error code '3': C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-npj5it\\cmTC_1f8c8.dir\\Debug\\vc143.pdb [C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-npj5it\\cmTC_1f8c8.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.58
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:56 (try_compile)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Check for working CXX compiler: C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/fromScratch/HVNC/build/CMakeFiles/CMakeScratch/TryCompile-3fn8ck"
      binary: "C:/Users/<USER>/OneDrive/Desktop/fromScratch/HVNC/build/CMakeFiles/CMakeScratch/TryCompile-3fn8ck"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/fromScratch/HVNC/build/CMakeFiles/CMakeScratch/TryCompile-3fn8ck'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_49203.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 7/11/2025 10:11:41 AM.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3fn8ck\\cmTC_49203.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_49203.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3fn8ck\\Debug\\".
          Creating directory "cmTC_49203.dir\\Debug\\cmTC_49203.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_49203.dir\\Debug\\cmTC_49203.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_49203.dir\\Debug\\cmTC_49203.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_49203.dir\\Debug\\\\" /Fd"cmTC_49203.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3fn8ck\\testCXXCompiler.cxx"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_49203.dir\\Debug\\\\" /Fd"cmTC_49203.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3fn8ck\\testCXXCompiler.cxx"
          testCXXCompiler.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3fn8ck\\Debug\\cmTC_49203.exe" /INCREMENTAL /ILK:"cmTC_49203.dir\\Debug\\cmTC_49203.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/fromScratch/HVNC/build/CMakeFiles/CMakeScratch/TryCompile-3fn8ck/Debug/cmTC_49203.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/fromScratch/HVNC/build/CMakeFiles/CMakeScratch/TryCompile-3fn8ck/Debug/cmTC_49203.lib" /MACHINE:X64  /machine:x64 cmTC_49203.dir\\Debug\\testCXXCompiler.obj
          cmTC_49203.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3fn8ck\\Debug\\cmTC_49203.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_49203.dir\\Debug\\cmTC_49203.tlog\\unsuccessfulbuild".
          Touching "cmTC_49203.dir\\Debug\\cmTC_49203.tlog\\cmTC_49203.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\fromScratch\\HVNC\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3fn8ck\\cmTC_49203.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.92
        
      exitCode: 0
...
