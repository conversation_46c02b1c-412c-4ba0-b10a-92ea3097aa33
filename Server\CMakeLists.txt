# Server executable CMakeLists.txt
set(SERVER_SOURCES
    Main.cpp
    Server.cpp
    ControlWindow.cpp
)

set(SERVER_HEADERS
    Common.h
    Server.h
    ControlWindow.h
    _version.h
)

# Create Server executable
add_executable(Server WIN32 ${SERVER_SOURCES} ${SERVER_HEADERS})

# Link with common library
target_link_libraries(Server common)

# Set include directories
target_include_directories(Server PRIVATE 
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/common
)

# Set Windows-specific properties
if(WIN32)
    set_target_properties(Server PROPERTIES
        WIN32_EXECUTABLE TRUE
    )
    
    # Link additional Windows libraries
    target_link_libraries(Server
        ws2_32
        user32
        kernel32
        gdi32
        shell32
    )
endif()

# Set compiler-specific options
if(MSVC)
    target_compile_options(Server PRIVATE /MT$<$<CONFIG:Debug>:d>)
    target_compile_definitions(Server PRIVATE WIN32 _WINDOWS UNICODE _UNICODE)
endif()

# Set output directory
set_target_properties(Server PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY_DEBUG "${CMAKE_SOURCE_DIR}/_bin/Debug/Win32"
    RUNTIME_OUTPUT_DIRECTORY_RELEASE "${CMAKE_SOURCE_DIR}/_bin/Release/Win32"
)
