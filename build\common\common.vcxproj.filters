﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\fromScratch\HVNC\common\Api.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\fromScratch\HVNC\common\HTTP.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\fromScratch\HVNC\common\Panel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\fromScratch\HVNC\common\Utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\fromScratch\HVNC\common\Api.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\fromScratch\HVNC\common\Common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\fromScratch\HVNC\common\HTTP.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\fromScratch\HVNC\common\Inject.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\fromScratch\HVNC\common\Panel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\fromScratch\HVNC\common\Utils.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\fromScratch\HVNC\common\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{A8B97C5A-CAFF-3ADD-BD52-5E8060481128}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{94810210-C078-35F4-89D2-B895C063515F}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
